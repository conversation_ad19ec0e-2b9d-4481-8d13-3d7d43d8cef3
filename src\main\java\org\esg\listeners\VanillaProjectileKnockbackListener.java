package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.util.Vector;
import org.esg.Manager.KnockbackImmunityManager;
import org.esg.utils.KnockbackUtil;
import org.esg.utils.WeaponUtils;
import org.esg.models.Weapon;

/**
 * Listener para gerenciar knockback de projéteis vanilla (como flechas).
 * Este listener garante que o knockback seja calculado corretamente
 * baseado na localização do atirador, não do projétil.
 */
public class VanillaProjectileKnockbackListener implements Listener {

    /**
     * Aplica knockback customizado para projéteis vanilla.
     * Este método é executado com alta prioridade para interceptar
     * o knockback antes que o sistema vanilla o aplique.
     */
    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onProjectileDamage(EntityDamageByEntityEvent event) {
        // Verificar se é dano de projétil em um jogador
        if (!(event.getEntity() instanceof Player) || !(event.getDamager() instanceof Projectile)) {
            return;
        }

        Player victim = (Player) event.getEntity();
        Projectile projectile = (Projectile) event.getDamager();

        // Verificar se o atirador é um jogador
        if (!(projectile.getShooter() instanceof Player)) {
            return;
        }

        Player shooter = (Player) projectile.getShooter();

        // Não aplicar knockback se o jogador atirou em si mesmo
        // (isso evita problemas de direção quando o jogador se atira)
        if (shooter.equals(victim)) {
            // Cancelar o knockback vanilla para evitar comportamento estranho
            event.setDamage(event.getDamage()); // Manter o dano
            // O knockback será cancelado naturalmente pois não aplicamos nenhum
            return;
        }

        // Verificar se a vítima tem imunidade a knockback
        if (KnockbackImmunityManager.isImmune(victim)) {
            return;
        }

        // Verificar se o atirador está usando uma arma customizada
        Weapon weapon = WeaponUtils.getWeaponFromItem(shooter.getInventory().getItemInHand(), shooter);
        
        Vector knockback;
        if (weapon != null) {
            // Se está usando uma arma customizada, usar o knockback da arma
            knockback = KnockbackUtil.getKnockback(victim.getLocation(), shooter, weapon.getName());
            // Aplicar imunidade baseada na arma
            KnockbackImmunityManager.applyImmunity(victim, weapon.getName(), weapon.getType());
        } else {
            // Se não está usando arma customizada, usar knockback padrão
            knockback = KnockbackUtil.getKnockback(victim.getLocation(), shooter);
            // Aplicar imunidade padrão para projéteis vanilla
            KnockbackImmunityManager.applyImmunity(victim, "Vanilla Arrow", org.esg.enums.WeaponType.RIFLE);
        }

        // Aplicar o knockback customizado
        // Usar runTaskLater para aplicar após o dano vanilla ser processado
        org.bukkit.Bukkit.getScheduler().runTaskLater(org.esg.Main.getPlugin(), () -> {
            if (victim.isOnline() && knockback != null) {
                victim.setVelocity(knockback);
            }
        }, 1L);
    }
}
