package org.esg.listeners;

import org.bukkit.entity.Player;
import org.bukkit.entity.Projectile;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.util.Vector;
import org.esg.Manager.KnockbackImmunityManager;
import org.esg.utils.KnockbackUtil;
import org.esg.utils.WeaponUtils;
import org.esg.models.Weapon;

/**
 * Listener para gerenciar knockback de projéteis vanilla (como flechas).
 * Este listener garante que o knockback seja calculado corretamente
 * baseado na localização do atirador, não do projétil.
 */
public class VanillaProjectileKnockbackListener implements Listener {

    /**
     * Intercepta e aplica knockback customizado para projéteis vanilla.
     * Este método é executado com prioridade HIGHEST para garantir que seja o primeiro a processar.
     */
    @EventHandler(priority = EventPriority.HIGHEST, ignoreCancelled = true)
    public void onProjectileDamage(EntityDamageByEntityEvent event) {
        // Verificar se é dano de projétil em um jogador
        if (!(event.getEntity() instanceof Player) || !(event.getDamager() instanceof Projectile)) {
            return;
        }

        Player victim = (Player) event.getEntity();
        Projectile projectile = (Projectile) event.getDamager();

        // Verificar se o atirador é um jogador
        if (!(projectile.getShooter() instanceof Player)) {
            return;
        }

        Player shooter = (Player) projectile.getShooter();

        // Para TODOS os casos de projétil de jogador, cancelar o knockback vanilla
        // e aplicar nosso sistema customizado

        // Salvar a velocidade atual antes do knockback vanilla
        final Vector originalVelocity = victim.getVelocity().clone();

        // Usar runTaskLater para restaurar a velocidade e aplicar nosso knockback
        org.bukkit.Bukkit.getScheduler().runTaskLater(org.esg.Main.getPlugin(), () -> {
            if (!victim.isOnline()) return;

            // Primeiro, cancelar qualquer knockback vanilla que possa ter sido aplicado
            victim.setVelocity(originalVelocity);

            // Agora aplicar nosso knockback customizado (se aplicável)
            if (shooter.equals(victim)) {
                // Caso especial: jogador atirou em si mesmo - sem knockback
                victim.setVelocity(new Vector(0, originalVelocity.getY(), 0)); // Manter apenas Y para gravidade
            } else if (!KnockbackImmunityManager.isImmune(victim)) {
                // Aplicar knockback customizado baseado na posição do atirador
                Weapon weapon = WeaponUtils.getWeaponFromItem(shooter.getInventory().getItemInHand(), shooter);
                Vector customKnockback;

                if (weapon != null) {
                    customKnockback = KnockbackUtil.getKnockback(victim.getLocation(), shooter, weapon.getName());
                    KnockbackImmunityManager.applyImmunity(victim, weapon.getName(), weapon.getType());
                } else {
                    customKnockback = KnockbackUtil.getKnockback(victim.getLocation(), shooter);
                    KnockbackImmunityManager.applyImmunity(victim, "Vanilla Arrow", org.esg.enums.WeaponType.RIFLE);
                }

                if (customKnockback != null) {
                    victim.setVelocity(customKnockback);
                }
            }
        }, 1L);
    }
}
